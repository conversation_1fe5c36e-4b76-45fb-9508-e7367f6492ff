<?php
/**
 * Security Utilities Class
 * Provides methods for input validation, output sanitization, and SQL injection prevention
 */
class SecurityUtils {
    
    /**
     * Sanitize input data to prevent XSS and injection attacks
     * @param string $input Raw input data
     * @return string Sanitized input
     */
    public static function sanitizeInput($input) {
        if (empty($input)) {
            return '';
        }
        
        // Remove null bytes
        $input = str_replace(chr(0), '', $input);
        
        // Strip whitespace and basic sanitization
        $input = trim($input);
        
        // Remove potential SQL injection patterns
        $dangerous_patterns = array(
            '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)\s/i',
            '/(\s|^)(script|javascript|vbscript|onload|onerror|onclick)/i',
            '/[<>]/',
            '/[\'"]/i'
        );
        
        foreach ($dangerous_patterns as $pattern) {
            $input = preg_replace($pattern, '', $input);
        }
        
        return $input;
    }
    
    /**
     * Sanitize output data to prevent XSS
     * @param string $output Raw output data
     * @return string Sanitized output
     */
    public static function sanitizeOutput($output) {
        if (empty($output)) {
            return '';
        }
        
        return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate date format (DD-MM-YYYY)
     * @param string $date Date string to validate
     * @return bool True if valid, false otherwise
     */
    public static function validateDateFormat($date) {
        if (empty($date)) {
            return false;
        }
        
        // Check format DD-MM-YYYY
        if (!preg_match('/^(\d{2})-(\d{2})-(\d{4})$/', $date, $matches)) {
            return false;
        }
        
        $day = (int)$matches[1];
        $month = (int)$matches[2];
        $year = (int)$matches[3];
        
        // Validate date components
        if ($day < 1 || $day > 31 || $month < 1 || $month > 12 || $year < 1900 || $year > 2100) {
            return false;
        }
        
        // Check if date is valid
        return checkdate($month, $day, $year);
    }
    
    /**
     * Validate BA number format
     * @param string $ba_number BA number to validate
     * @return bool True if valid, false otherwise
     */
    public static function validateBANumber($ba_number) {
        if (empty($ba_number)) {
            return false;
        }
        
        // BA number should be alphanumeric with some special characters
        // Allow letters, numbers, hyphens, underscores, and forward slashes
        return preg_match('/^[A-Z0-9\-_\/]+$/i', $ba_number);
    }
    
    /**
     * Validate vendor number format
     * @param string $vendor_number Vendor number to validate
     * @return bool True if valid, false otherwise
     */
    public static function validateVendorNumber($vendor_number) {
        if (empty($vendor_number)) {
            return false;
        }
        
        // Vendor number should be numeric
        return preg_match('/^\d{10}$/', $vendor_number);
    }
    
    /**
     * Validate status value
     * @param string $status Status value to validate
     * @return bool True if valid, false otherwise
     */
         public static function validateStatus($status) {
         $valid_statuses = array('', '10', '20', '30', '40', '50', '50-');
         return in_array($status, $valid_statuses, true);
     }
    
    /**
     * Generate CSRF token for form security
     * @return string CSRF token HTML input
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return '<input type="hidden" name="csrf_token" value="' . $_SESSION['csrf_token'] . '">';
    }
    
    /**
     * Validate CSRF token
     * @param string $token Token to validate
     * @return bool True if valid, false otherwise
     */
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Prepare SQL query with parameter binding for Oracle
     * @param resource $conn Database connection
     * @param string $sql SQL query with named parameters
     * @param array $params Associative array of parameters
     * @return resource|false Prepared statement or false on failure
     */
         public static function prepareSQLQuery($conn, $sql, $params = array()) {
        $query = oci_parse($conn, $sql);
        
        if (!$query) {
            return false;
        }
        
        // Bind parameters
        foreach ($params as $param => $value) {
            if (!oci_bind_by_name($query, $param, $params[$param])) {
                return false;
            }
        }
        
        return $query;
    }
    
    /**
     * Execute prepared statement safely
     * @param resource $query Prepared statement
     * @return bool True on success, false on failure
     */
    public static function executePreparedQuery($query) {
        return oci_execute($query);
    }
    
    /**
     * Log security events
     * @param string $event_type Type of security event
     * @param string $details Event details
     * @param string $user_id User ID if available
     */
    public static function logSecurityEvent($event_type, $details, $user_id = '') {
        $log_entry = array(
            'timestamp' => date('Y-m-d H:i:s'),
            'event_type' => $event_type,
            'details' => $details,
            'user_id' => $user_id,
            'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown'
        );
        
        // Log to file or database
        error_log("SECURITY_EVENT: " . json_encode($log_entry));
    }
    
    /**
     * Rate limiting to prevent brute force attacks
     * @param string $key Unique key for rate limiting
     * @param int $max_attempts Maximum attempts allowed
     * @param int $time_window Time window in seconds
     * @return bool True if within limits, false if exceeded
     */
    public static function checkRateLimit($key, $max_attempts = 5, $time_window = 300) {
        if (!isset($_SESSION['rate_limit'])) {
            $_SESSION['rate_limit'] = array();
        }
        
        $current_time = time();
        $rate_key = $key . '_' . $_SERVER['REMOTE_ADDR'];
        
        if (!isset($_SESSION['rate_limit'][$rate_key])) {
            $_SESSION['rate_limit'][$rate_key] = array(
                'attempts' => 1,
                'first_attempt' => $current_time
            );
            return true;
        }
        
        $rate_data = $_SESSION['rate_limit'][$rate_key];
        
        // Reset if time window has passed
        if (($current_time - $rate_data['first_attempt']) > $time_window) {
            $_SESSION['rate_limit'][$rate_key] = array(
                'attempts' => 1,
                'first_attempt' => $current_time
            );
            return true;
        }
        
        // Check if exceeded max attempts
        if ($rate_data['attempts'] >= $max_attempts) {
            self::logSecurityEvent('RATE_LIMIT_EXCEEDED', "Key: $key, IP: " . $_SERVER['REMOTE_ADDR']);
            return false;
        }
        
        // Increment attempts
        $_SESSION['rate_limit'][$rate_key]['attempts']++;
        return true;
    }
    
    /**
     * Validate Oracle parameter binding type
     * @param mixed $value Value to validate
     * @return string Oracle binding type (SQLT_CHR, SQLT_INT, etc.)
     */
    public static function getOracleBindingType($value) {
        if (is_int($value)) {
            return SQLT_INT;
        } elseif (is_float($value)) {
            return SQLT_FLT;
        } elseif (is_string($value)) {
            return SQLT_CHR;
        } else {
            return SQLT_CHR; // Default to character
        }
    }
    
    /**
     * Create secure database query builder
     * @param array $base_conditions Base WHERE conditions
     * @param array $dynamic_conditions Dynamic conditions with parameters
     * @return array Array containing SQL query and bind parameters
     */
    public static function buildSecureQuery($base_conditions, $dynamic_conditions = array()) {
        $where_clause = implode(' AND ', $base_conditions);
        $bind_params = array();
        
        if (!empty($dynamic_conditions)) {
            $dynamic_where = array();
            foreach ($dynamic_conditions as $condition) {
                if (isset($condition['clause']) && isset($condition['param']) && isset($condition['value'])) {
                    $dynamic_where[] = $condition['clause'];
                    $bind_params[$condition['param']] = $condition['value'];
                }
            }
            
            if (!empty($dynamic_where)) {
                $where_clause .= ' AND ' . implode(' AND ', $dynamic_where);
            }
        }
        
        return array(
            'where_clause' => $where_clause,
            'bind_params' => $bind_params
        );
    }
}
?> 