<?php
ob_start();
session_start();
include('../include/ex_fungsi.php');
include('../include/validasi.php');
require_once ('../security_helper.php');
require_once ('security_utils.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$halaman_id = 4872;
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];

// Security: Validate user session and permissions
if (empty($user_id) || empty($user_org)) {
    header('Location: ../login.php');
    exit();
}

$orgIn = $user_org;
$page = "list_ba_secure.php";

$vendor = $fungsi->ex_find_vendor($conn, $user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

// Security: Input validation and sanitization
$tanggal_mulai = isset($_POST['tanggal_mulai']) ? SecurityUtils::sanitizeInput($_POST['tanggal_mulai']) : '';
$tanggal_selesai = isset($_POST['tanggal_selesai']) ? SecurityUtils::sanitizeInput($_POST['tanggal_selesai']) : '';
$no_ba = isset($_POST['no_ba']) ? SecurityUtils::sanitizeInput($_POST['no_ba']) : '';
$statuse = isset($_POST['statuse']) ? SecurityUtils::sanitizeInput($_POST['statuse']) : '';

// Security: Validate date format
if (!empty($tanggal_mulai) && !SecurityUtils::validateDateFormat($tanggal_mulai)) {
    $tanggal_mulai = '';
}
if (!empty($tanggal_selesai) && !SecurityUtils::validateDateFormat($tanggal_selesai)) {
    $tanggal_selesai = '';
}

// Security: Validate BA number format
if (!empty($no_ba) && !SecurityUtils::validateBANumber($no_ba)) {
    $no_ba = '';
}

$currentPage = "list_ba_secure.php";
$komen = "";

if (isset($_POST['cari'])) {
    $_SESSION['tgl_mulai_inv'] = $tanggal_mulai;
    $_SESSION['tgl_end_inv'] = $tanggal_selesai;
    $_SESSION['no_inv'] = isset($_POST['no_invoice']) ? $_POST['no_invoice'] : '';
    
    // Prepared statement implementation
    $sql_params = array();
    $bind_params = array();
    
    if (empty($vendor) && empty($tanggal_mulai) && empty($tanggal_selesai) && empty($no_ba) && empty($statuse)) {
        // Default query with prepared statement
        $sql = "
            SELECT DISTINCT A.ORG, A.NO_BA, A.NO_VENDOR, A.NAMA_VENDOR, A.NO_PAJAK_EX, 
                   A.TGL_INVOICE, A.TGL_BA, A.KLAIM_SEMEN, A.KLAIM_KTG, A.PDPKS, A.PDPKK, 
                   A.PAJAK_INV, A.TOTAL_INV, A.NO_BAF, A.STATUS_BA, A.FILENAME, 
                   TO_CHAR(A.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1, B.STATUS_BA_INVOICE, C.WARNA_PLAT 
            FROM EX_BA A 
            LEFT JOIN EX_BA_INVOICE B ON B.NO_BA = A.NO_BA AND B.DIPAKAI = 1 
            LEFT JOIN EX_TRANS_HDR C ON A.NO_BA = C.NO_BA 
            WHERE A.DELETE_MARK = '0'
                AND A.ORG = :org_param
                AND A.NO_BA IS NOT NULL
                AND (C.WARNA_PLAT IS NOT NULL OR C.WARNA_PLAT != '')
                AND A.STATUS_BA = '50'
                AND B.STATUS_BA_INVOICE IS NULL
                AND (B.STATUS_BA_INVOICE IS NULL OR B.STATUS_BA_INVOICE = '10' 
                     OR B.STATUS_BA_INVOICE = '30' OR B.STATUS_BA_INVOICE = '40' 
                     OR B.STATUS_BA_INVOICE = '50')
            ORDER BY A.ORG, A.NO_VENDOR, A.NO_BA DESC
        ";
        $bind_params[':org_param'] = $orgIn;
    } else {
        // Dynamic query building with prepared statement
        $sql = "
            SELECT DISTINCT A.ORG, A.NO_BA, A.NO_VENDOR, A.NAMA_VENDOR, A.NO_PAJAK_EX, 
                   A.TGL_INVOICE, A.TGL_BA, A.KLAIM_SEMEN, A.KLAIM_KTG, A.PDPKS, A.PDPKK, 
                   A.PAJAK_INV, A.TOTAL_INV, A.NO_BAF, A.STATUS_BA, A.FILENAME, 
                   TO_CHAR(A.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1, B.STATUS_BA_INVOICE, C.WARNA_PLAT 
            FROM EX_BA A 
            LEFT JOIN EX_BA_INVOICE B ON B.NO_BA = A.NO_BA AND B.DIPAKAI = 1 
            LEFT JOIN EX_TRANS_HDR C ON A.NO_BA = C.NO_BA 
            WHERE (C.WARNA_PLAT IS NOT NULL OR C.WARNA_PLAT != '') 
                AND B.STATUS_BA_INVOICE IS NULL
        ";
        
        $conditions = array();
        
        // Security: Use prepared statement for vendor filter
        if (!empty($vendor)) {
            $conditions[] = "A.NO_VENDOR LIKE :vendor_param";
            $bind_params[':vendor_param'] = $vendor;
        }
        
        // Security: Use prepared statement for date range
        if (!empty($tanggal_mulai) || !empty($tanggal_selesai)) {
            $tanggal_mulai_sql = !empty($tanggal_mulai) ? $tanggal_mulai : '01-01-1990';
            $tanggal_selesai_sql = !empty($tanggal_selesai) ? $tanggal_selesai : '12-12-9999';
            
            $conditions[] = "A.TGL_BA BETWEEN TO_DATE(:tgl_mulai, 'DD-MM-YYYY') AND TO_DATE(:tgl_selesai, 'DD-MM-YYYY')";
            $bind_params[':tgl_mulai'] = $tanggal_mulai_sql;
            $bind_params[':tgl_selesai'] = $tanggal_selesai_sql;
        }
        
        // Security: Use prepared statement for BA number
        if (!empty($no_ba)) {
            $conditions[] = "A.NO_BA LIKE :no_ba_param";
            $bind_params[':no_ba_param'] = $no_ba;
        }
        
        // Security: Use prepared statement for status
        if ($statuse == "50-") {
            $conditions[] = "B.STATUS_BA_INVOICE IS NULL";
        } elseif ($statuse == "10") {
            $conditions[] = "B.STATUS_BA_INVOICE = :status_param";
            $bind_params[':status_param'] = '10';
        } elseif ($statuse == "50") {
            $conditions[] = "B.STATUS_BA_INVOICE = :status_param";
            $bind_params[':status_param'] = '20';
        } else {
            $conditions[] = "(B.STATUS_BA_INVOICE IS NULL OR B.STATUS_BA_INVOICE = '10' 
                            OR B.STATUS_BA_INVOICE = '20' OR B.STATUS_BA_INVOICE = '30' 
                            OR B.STATUS_BA_INVOICE = '40' OR B.STATUS_BA_INVOICE = '50')";
        }
        
        // Add conditions to SQL
        if (!empty($conditions)) {
            $sql .= " AND " . implode(" AND ", $conditions);
        }
        
        $sql .= "
            AND A.DELETE_MARK = '0'
            AND A.ORG = :org_param
            AND A.NO_BA IS NOT NULL
            AND A.STATUS_BA = '50'
            ORDER BY A.ORG, A.NO_VENDOR, A.NO_BA DESC
        ";
        
        $bind_params[':org_param'] = $orgIn;
    }
    
    // Security: Execute prepared statement
    try {
        $query = oci_parse($conn, $sql);
        
        if (!$query) {
            throw new Exception('Failed to prepare SQL statement');
        }
        
        // Bind parameters
        foreach ($bind_params as $param => $value) {
            if (!oci_bind_by_name($query, $param, $bind_params[$param])) {
                throw new Exception('Failed to bind parameter: ' . $param);
            }
        }
        
        // Execute query
        if (!oci_execute($query)) {
            throw new Exception('Failed to execute query');
        }
        
        // Security: Process results safely
        $results = array();
        while ($row = oci_fetch_array($query)) {
            $results[] = array(
                'org' => SecurityUtils::sanitizeOutput($row['ORG']),
                'no_ba' => SecurityUtils::sanitizeOutput($row['NO_BA']),
                'no_vendor' => SecurityUtils::sanitizeOutput($row['NO_VENDOR']),
                'nama_vendor' => SecurityUtils::sanitizeOutput($row['NAMA_VENDOR']),
                'no_pajak_ex' => SecurityUtils::sanitizeOutput($row['NO_PAJAK_EX']),
                'tgl_invoice' => $row['TGL_INVOICE'],
                'tgl_ba' => $row['TGL_BA'],
                'klaim_semen' => (float)$row['KLAIM_SEMEN'],
                'klaim_ktg' => (float)$row['KLAIM_KTG'],
                'pdpks' => (float)$row['PDPKS'],
                'pdpkk' => (float)$row['PDPKK'],
                'pajak_inv' => (float)$row['PAJAK_INV'],
                'total_inv' => (float)$row['TOTAL_INV'],
                'no_baf' => SecurityUtils::sanitizeOutput($row['NO_BAF']),
                'status_ba' => SecurityUtils::sanitizeOutput($row['STATUS_BA']),
                'status_ba_invoice' => SecurityUtils::sanitizeOutput($row['STATUS_BA_INVOICE']),
                'filename' => SecurityUtils::sanitizeOutput($row['FILENAME']),
                'warna_plat' => SecurityUtils::sanitizeOutput($row['WARNA_PLAT'])
              );
        }
        
        $total = count($results);
        if ($total < 1) {
            $komen = "Tidak Ada Data Yang Ditemukan";
        }
        
    } catch (Exception $e) {
        // Security: Log error without exposing sensitive information
        error_log("Database query error: " . $e->getMessage());
        $komen = "Terjadi kesalahan dalam pencarian data";
        $total = 0;
        $results = array();
    }
}

// XSS
function safe_html($value) {
    return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
}
?>

<script language="javascript">
var message = "You do not have permission to right click";

function clickIE() {
    if (document.all) {
        alert(message);
        return false;
    }
}

function clickNS(e) {
    if (document.layers || (document.getElementById && !document.all)) {
        if (e.which == 2 || e.which == 3) {
            alert(message);
            return false;
        }
    }
}

if (document.layers) {
    document.captureEvents(Event.MOUSEDOWN);
    document.onmousedown = clickNS;
} else {
    document.onmouseup = clickNS;
    document.oncontextmenu = clickIE;
}

document.oncontextmenu = new Function("return false");
</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim</title>
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<script type="text/javascript" language="JavaScript">
    document.write('<div id="tunggu_ya" style="display:none"><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Cargando datos....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
</script>

<div id="halaman_tampil" style="display:inline">
    <div align="center">
        <table width="600" align="center" class="adminheading" border="0">
            <tr>
                <th class="kb2">Daftar BA Rekapitulasi</th>
            </tr>
        </table>
    </div>

    <?php if (!isset($total) || $total < 1): ?>
    <div align="center">
        <table width="600" align="center" class="adminlist">
            <tr>
                <th align="left" colspan="4">&nbsp;Form Search BA Rekapitulasi</th>
            </tr>
        </table>
    </div>

    <form id="form1" name="form1" method="post" action="<?= safe_html($page); ?>">
        <table width="600" align="center" class="adminform">
            <tr width="174">
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr width="174">
                <td class="puso">No Berita Acara</td>
                <td class="puso">:</td>
                <td><input type="text" id="no_ba" name="no_ba" value="<?= safe_html($no_ba); ?>" maxlength="50" /></td>
            </tr>
            <tr>
                <td class="puso">Periode Berita Acara</td>
                <td class="puso">:</td>
                <td>
                    <input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?= $hanya_baca; ?> value="<?= safe_html($tanggal_mulai); ?>" />
                    <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
                    &nbsp;&nbsp;&nbsp;hasta&nbsp;&nbsp;&nbsp;
                    <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?= $hanya_baca; ?> value="<?= safe_html($tanggal_selesai); ?>" />
                    <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." />
                </td>
            </tr>
            <tr>
                <td class="puso">Status</td>
                <td class="puso">:</td>
                <td>
                    <select name="statuse" id="statuse">
                        <option value="">- Pilih Status -</option>
                        <option value="50-" <?= ($statuse == '50-') ? 'selected' : ''; ?>>BA Approved</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="ThemeOfficeMenu">&nbsp;</td>
                <td class="ThemeOfficeMenu">&nbsp;</td>
                <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" /></td>
            </tr>
            <tr>
                <td class="ThemeOfficeMenu">&nbsp;</td>
                <td class="ThemeOfficeMenu">&nbsp;</td>
            </tr>
        </table>
    </form>
    <?php endif; ?>

    <br /><br />

    <?php if (isset($total) && $total > 0): ?>
    <form id="data_claim" name="data_claim" method="post" action="komentar.php">
        <div align="center">
            <table width="95%" align="center" class="adminlist">
                <tr>
                    <th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA Rekapitulasi</span></th>
                </tr>
            </table>
        </div>
        
        <div align="center">
            <table width="95%" align="center" class="adminlist" id="myScrollTable">
                <thead>
                    <tr class="quote">
                        <td align="center"><strong>No.</strong></td>
                        <td align="center"><strong>Org</strong></td>
                        <td align="center"><strong>BA Rekapitulasi</strong></td>
                        <td align="center"><strong>Tgl BA</strong></td>
                        <td align="center"><strong>Total SPJ</strong></td>
                        <td align="center"><strong>Status</strong></td>
                        <td align="center"><strong>Vendor</strong></td>
                        <td align="center"><strong>Warna Plat</strong></td>
                        <td align="center"><strong>Aksi</strong></td>
                    </tr>
                </thead>
                <tbody>
                    <?php for ($i = 0; $i < $total; $i++): ?>
                        <?php 
                        $b = $i + 1;
                        $orgCom = "orgke" . $i;
                        $rowClass = (($i % 2) == 0) ? 'row0' : 'row1';
                        ?>
                        
                        <tr class="<?= $rowClass; ?>">
                            <td align="center"><?= $b; ?></td>
                            <td align="center">
                                <?= safe_html($results[$i]['org']); ?>
                                <input name="<?= safe_html($orgCom); ?>" id="<?= safe_html($orgCom); ?>" type="hidden" value="<?= safe_html($results[$i]['org']); ?>" />
                            </td>
                            <td align="center">
                                <a href="javascript:popUp('../ex_ba_sp/upload/<?= safe_html($results[$i]['filename']); ?>')">
                                    <?= safe_html($results[$i]['no_ba']); ?>
                                </a>
                            </td>
                            <td align="center"><?= safe_html($results[$i]['tgl_ba']); ?></td>
                            <td align="center"><?= number_format($results[$i]['total_inv'] + $results[$i]['pajak_inv'], 0, ",", "."); ?></td>
                            <td align="center">
                                <?php
                                if (empty($results[$i]['status_ba_invoice'])) {
                                    if ($results[$i]['status_ba'] == '50') {
                                        echo 'BA Approved';
                                    }
                                } else {
                                    switch ($results[$i]['status_ba_invoice']) {
                                        case '10':
                                            echo 'Upload Invoice';
                                            break;
                                        case '20':
                                            echo 'Approved';
                                            break;
                                        case '30':
                                            echo 'Reverse';
                                            break;
                                        case '40':
                                            echo 'Rejected';
                                            break;
                                        case '50':
                                            echo 'Approved';
                                            break;
                                        default:
                                            echo safe_html($results[$i]['status_ba_invoice']);
                                    }
                                }
                                ?>
                            </td>
                            <td align="center"><?= safe_html($results[$i]['nama_vendor']); ?></td>
                            <td align="center"><?= safe_html($results[$i]['warna_plat']); ?></td>
                            <td align="center">
                                <?php
                                if (empty($results[$i]['status_ba_invoice'])) {
                                    if ($results[$i]['status_ba'] == '50') {
                                        echo '<a href="create_invoice_ba.php?no_ba=' . urlencode($results[$i]['no_ba']) . '">Create</a>';
                                    }
                                } else {
                                    if ($results[$i]['status_ba_invoice'] == '50') {
                                        echo '<a href="detail_invoice_ba.php?no_ba=' . urlencode($results[$i]['no_ba']) . '">Detail</a>';
                                    }
                                }
                                ?>
                            </td>
                        </tr>
                    <?php endfor; ?>
                    
                    <tr class="quote">
                        <td colspan="9" align="center" style="padding: 8px;">
                            <a href="<?= safe_html($page); ?>" class="button">Volver</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </form>
    <?php endif; ?>

    <div align="center">
        <?= safe_html($komen); ?>
    </div>
</div>

<?php if (isset($total) && $total > 11): ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<?php endif; ?>

<?php include('../include/ekor.php'); ?>

<script language="javascript">
    obj = document.getElementById("tunggu_ya");
    obj.style.display = "none";
    obj_tampil = document.getElementById("halaman_tampil");
    obj_tampil.style.display = "inline";
</script>

</body>
</html> 