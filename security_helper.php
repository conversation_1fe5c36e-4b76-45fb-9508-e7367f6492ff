<?php
require_once ('pgr_sanitizer.php');

function sanitize_global_input(){
    if (!empty($_REQUEST)) {
        $_REQUEST = sanitize_input($_REQUEST, TRUE);
    }   

    if (!empty($_POST)) {
        $_POST= sanitize_input($_POST, TRUE);
    }

    if (!empty($_GET)) {
        $_GET= sanitize_input($_GET, TRUE);
    }

    if (!empty($_COOKIE)) {
        $_COOKIE= sanitize_input($_COOKIE, TRUE);
    }
}

function is_valid_date($date) {
  if (empty($date)) return true; // Empty dates are allowed
  if (!preg_match('/^\d{2}-\d{2}-\d{4}$/', $date)) return false;
  
  $parts = explode('-', $date);
  return checkdate($parts[1], $parts[0], $parts[2]);
}